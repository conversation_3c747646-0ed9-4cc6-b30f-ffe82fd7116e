﻿{
  "$schema": "https://json.schemastore.org/launchsettings.json",
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:2699",
      "sslPort": 44346
    }
  },
  "profiles": {
    "PayPing.Settlement.WebApi": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7280;http://localhost:5234",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "Settlement_Sentry_DSN": "https://<EMAIL>/39",
        "PayPing_Ipg_Address": "https://api.payping.dev/pay",
        "PayPing_Report_Address": "https://api.payping.dev/pay",
        "PayPing_UserServices_Address": "https://api.payping.dev/userservice",
        "PayPing_Internal_Token_Address": "https://oauth.payping.dev",
        "PayPing_PPNG_Address": "https://api.payping.dev/ppng",
        "PayPing_Integrations_Address": "https://api.payping.dev/intergation",
        "RabbitMq": "rabbitmq://localhost",
        "RabbitMqPassword": "guest",
        "RabbitMqUsername": "guest",
        "ExPayBaseHost": "",
        "EzPayToken": "",
        "Proxy_Host": "",
        "Proxy_UserName": "",
        "Proxy_Password": "",
        "Wallet_Grpc_Address": "http://localhost:5951",
        "ConcurrencyRedis": "127.0.0.1",
        "EFRedisConectionString": "127.0.0.1",
        "REDIS_HOST": "127.0.0.1",
        "REDIS_PORT": "6379",
        "RedisClusterConnectionString": "127.0.0.1:6379",
        "RedisInstanceName": "master",
        "SettlementDb": "",
        "Logstash_Type": "TCP",
        "Logstash_Address": "tcp://localhost",
        "Logstash_Port": "9513",
        "PayPingConfiguration__TopLevelDomain": "dev",
        "PayPingConfiguration__DashbaordRequestPagePPNGFormat": "https://ppng.{0}/t/{1}",
        "PayPingConfiguration__DashboardRequestPageUrlFormat": "https://app.payping.{0}/settlement/report/{1}"
      }
    },
    "Api": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7098;http://localhost:5206",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
