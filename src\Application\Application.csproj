﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<AssemblyName>Zify.Settlement.$(MSBuildProjectName)</AssemblyName>
		<RootNamespace>$(AssemblyName)</RootNamespace>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<NoWarn>$(NoWarn);1591</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<None Remove="Domain\UserWalletInformation.cs~RFae0e88.TMP" />
	</ItemGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Ardalis.GuardClauses" Version="5.0.0" />
		<PackageReference Include="Asp.Versioning.Http" Version="8.1.0" />
		<PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
		<PackageReference Include="DispatchR.Mediator" Version="1.3.0" />
		<PackageReference Include="DNTPersianUtils.Core" Version="6.6.1" />
		<PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
		<PackageReference Include="IdentityModel.AspNetCore.AccessTokenValidation" Version="1.0.0-preview.3" />
		<PackageReference Include="IdentityModel.AspNetCore.OAuth2Introspection" Version="6.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
		<PackageReference Include="ErrorOr" Version="2.0.1" />
		<PackageReference Include="FluentValidation" Version="12.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.6" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
		<PackageReference Include="Otp.NET" Version="1.4.0" />
		<PackageReference Include="PayPing.Auth.AccessManagement" Version="1.0.1" />
		<PackageReference Include="PayPing.Common.Tools" Version="5.0.2" />
		<PackageReference Include="PayPing.User.AdminSDK" Version="1.7.0" />
		<PackageReference Include="RedLock.net" Version="2.3.2" />
		<PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
		<PackageReference Include="Serilog" Version="4.3.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
		<PackageReference Include="Serilog.Enrichers.Span" Version="3.1.0" />
		<PackageReference Include="Serilog.Enrichers.Process" Version="3.0.0" />
		<PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
		<PackageReference Include="Serilog.Sinks.Elasticsearch" Version="10.0.0" />
		<PackageReference Include="Serilog.Formatting.Elasticsearch" Version="10.0.0" />
		<PackageReference Include="Serilog.Sinks.Network" Version="3.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Elastic.Apm.SerilogEnricher" Version="8.18.2" />
		<PackageReference Include="Elastic.CommonSchema.Serilog" Version="8.18.2" />
		<PackageReference Include="StackExchange.Redis" Version="2.8.41" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="9.0.1" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="9.0.1" />
		<PackageReference Include="System.Text.Encodings.Web" Version="9.0.6" />
		<PackageReference Include="Google.Protobuf" Version="3.31.1" />
		<PackageReference Include="Grpc.Net.Client" Version="2.71.0" />
		<PackageReference Include="Grpc.Net.ClientFactory" Version="2.71.0" />
		<PackageReference Include="Grpc.Tools" Version="2.72.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<Protobuf Include="Grpc\Protos\Wallet.proto" GrpcServices="Client" />
		<Protobuf Include="Grpc\Protos\WalletType.proto" GrpcServices="Client" />
		<Protobuf Include="Grpc\Protos\Coin.proto" GrpcServices="Client" />
		<Protobuf Include="Grpc\Protos\BaseExceptionResponse.proto" GrpcServices="None" />
		<Protobuf Include="Grpc\Protos\CustomTypes.proto" GrpcServices="None" />
	</ItemGroup>

</Project>
