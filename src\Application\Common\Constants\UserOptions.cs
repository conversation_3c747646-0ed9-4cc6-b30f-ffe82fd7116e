﻿namespace Zify.Settlement.Application.Common.Constants;

public static class UserOptions
{
     /// <summary>
    /// determined that whether this userid is for our wage account or not
    /// </summary>
    public static bool IsWageUser(int userId) =>
        userId == int.Parse(Environment.GetEnvironmentVariable("Settlement_Wage_UserId"));

    /// <summary>
    /// user info that our wage will send to that account
    /// </summary>
    public static int WageUserId => int.Parse(Environment.GetEnvironmentVariable("Settlement_Wage_UserId"));

    /// <summary>
    /// enable validation of ibans from bank APIs
    /// </summary>
    public static bool IsIbanInquiryActive =>
        bool.Parse(Environment.GetEnvironmentVariable("Enable_Iban_Inquiry") ?? "false");

    /// <summary>
    /// enable validation of iban banks that we support for account-to-account mode
    /// </summary>
    public static bool IsBankValidationActive =>
        bool.Parse(Environment.GetEnvironmentVariable("Enable_Bank_Validation") ?? "false");

    /// <summary>
    /// policy for amount of each settlement that user can create
    /// </summary>
    public static long GetMinSettlementAmount => long.Parse(Environment.GetEnvironmentVariable("MinSettlementAmount"));

    /// <summary>
    /// policy for amount of each settlement that user can create
    /// </summary>
    public static long GetMaxSettlementAmount => long.Parse(Environment.GetEnvironmentVariable("MaxSettlementAmount"));

    /// <summary>
    /// limitation for critical users such as crypto, ... not to settle more than 100M to specific iban in last 24hr
    /// </summary>
    public static decimal GetMaxLast24Amount => decimal.Parse(Environment.GetEnvironmentVariable("MaxLast24Amount"));

    /// <summary>
    /// Gets the default daily transfer limit for users.
    /// </summary>
    public static decimal DailyTransferDefaultLimit =>
        decimal.Parse(Environment.GetEnvironmentVariable("DailyTransferDefaultLimit") ?? "0");

    /// <summary>
    /// enable using finnotech service or not
    /// </summary>
    public static bool IsFinnotechServiceActive =>
        bool.Parse(Environment.GetEnvironmentVariable("Enable_Finnotech_Service") ?? "true");

    /// <summary>
    /// policy for maximum count of settlements that a user can create in a single request
    /// </summary>
    public static int GetMaxSettlementCountPerRequest =>
        int.Parse(Environment.GetEnvironmentVariable("MaxSettlementCountPerRequest"));

    /// <summary>
    /// Identical request creation limit time span in hours
    /// </summary>
    public static int IdenticalRequestLimitationHours =>
        int.Parse(Environment.GetEnvironmentVariable("IdenticalRequestLimitationHours") ?? "2");

    /// <summary>
    /// Time window in hours for IBAN update rate limiting
    /// </summary>
    public static int IbanUpdateTimeWindowHours =>
        int.Parse(Environment.GetEnvironmentVariable("IbanUpdateTimeWindowHours") ?? "24");

    /// <summary>
    /// Maximum number of IBAN changes allowed within the time window
    /// </summary>
    public static int IbanUpdateMaxChangesPerWindow =>
        int.Parse(Environment.GetEnvironmentVariable("IbanUpdateMaxChangesPerWindow") ?? "3");
}
