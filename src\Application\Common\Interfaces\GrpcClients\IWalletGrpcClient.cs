﻿using ErrorOr;
using Wallet;

namespace Zify.Settlement.Application.Common.Interfaces.GrpcClients;

public interface IWalletGrpcClient
{
    Task<ErrorOr<CreateWalletResponse>> CreateWallet(int userId,
        Guid creditCode,
        CancellationToken cancellationToken = default);

    Task<ErrorOr<GetWalletDetailByIdResponse>> GetWalletDetailsById(Guid walletId,
        CancellationToken cancellationToken = default);

    Task<ErrorOr<GetWalletTransactionTotalAmountResponse>> GetWalletTransactionTotalAmount(
        Guid walletId,
        TransactionType transactionType,
        CancellationToken cancellationToken = default);

    Task<ErrorOr<GetWalletBalanceByIdResponse>> GetWalletBalanceById(Guid walletId,
        CancellationToken cancellationToken = default);

    Task<ErrorOr<BlockWalletResponse>> Freeze(Guid walletId,
        Guid correlationId,
        decimal amount,
        CancellationToken cancellationToken = default);

    Task<ErrorOr<UnBlockWalletResponse>> Unfreeze(Guid walletId,
        Guid correlationId,
        CancellationToken cancellationToken = default);

    Task<ErrorOr<UnBlockWalletResponse>> UnfreezeAndWithdraw(Guid walletId,
        Guid correlationId,
        decimal amount,
        CancellationToken cancellationToken = default);

    Task<ErrorOr<DepositWalletResponse>> Deposit(Guid walletId,
        Guid correlationId,
        decimal amount,
        string description,
        CancellationToken cancellationToken = default);

    Task<ErrorOr<WithdrawWalletResponse>> Withdraw(Guid walletId,
        Guid correlationId,
        decimal amount,
        string description,
        CancellationToken cancellationToken = default);
}
