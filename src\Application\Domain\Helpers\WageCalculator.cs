﻿namespace Zify.Settlement.Application.Domain.Helpers;

public static class WageCalculator
{
    public static decimal CalculateWage(this UserConfig? userConfig, decimal amount)
    {
        if (userConfig == null)
            return CalculateWage(amount, 0, 0, 0, WageType.Percent);

        return userConfig.IsFree
            ? 0
            : CalculateWage(amount, userConfig.WageValue, userConfig.Min, userConfig.Max, userConfig.WageType);
    }

    private static decimal CalculateWage(decimal amount, decimal value, int min, int max, WageType type)
    {
        return type == WageType.Fixed ? (int)value : CalculatePercentageType(amount, value, min, max);
    }

    private static int CalculatePercentageType(decimal amount, decimal percent, int min, int max)
    {
        var defaultMinCommission = int.Parse(Environment.GetEnvironmentVariable("DefaultMinPercentageSettlementWage")!);

        if (min == 0 && percent == 0)
        {
            return defaultMinCommission;
        }

        var commission = amount * percent / 100;

        // If the "min" or "max" is equal to zero, it's not considered in the algorithm.
        if (min > 0 && commission < min) return min;

        if (max > 0 && commission > max) return max;

        // If the "max" parameter is equal to zero, The "default max" value will be set
        var defaultMaxCommission = int.Parse(Environment.GetEnvironmentVariable("DefaultMaxPercentageSettlementWage")!);

        if (max == 0 && commission > defaultMaxCommission) return defaultMaxCommission;

        return (int)Math.Floor(commission);
    }
}