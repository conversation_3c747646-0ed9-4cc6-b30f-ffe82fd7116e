using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.Orders;

public sealed record CancelOrderResponse;

public sealed class CancelOrderController : ApiControllerBase
{
    [HttpPut("cancel-order/{orderId:guid}")]
    [Authorize("write")]
    [ProducesResponseType<CancelOrderResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> CancelOrder(Guid orderId)
    {
        var command = new CancelOrderCommand(orderId);
        var result = await Mediator.Send(command, HttpContext.RequestAborted);
        return result.Match(Ok, Problem);
    }
}

public sealed record CancelOrderCommand(Guid OrderId)
    : IRequest<CancelOrderCommand, Task<ErrorOr<CancelOrderResponse>>>;

public sealed class CancelOrderCommandHandler(
    ApplicationDbContext dbContext,
    ICurrentUserService currentUserService)
    : IRequestHandler<CancelOrderCommand, Task<ErrorOr<CancelOrderResponse>>>
{
    public async Task<ErrorOr<CancelOrderResponse>> Handle(
        CancelOrderCommand request,
        CancellationToken cancellationToken)
    {
        var currentUser = currentUserService.UserId;
        if (currentUser == null)
            return Error.Unauthorized("شما مجوز انجام این عملیات را ندارید.");

        var order = await dbContext.Orders
            .AsTracking()
            .Where(o => o.Id == request.OrderId)
            .FirstOrDefaultAsync(cancellationToken);

        if (order is null)
            return Error.NotFound(description: "درخواست مورد نظر یافت نشد.");

        order.Cancel();

        var result = await dbContext.SaveChangesAsync(cancellationToken);

        return result > 0
            ? new CancelOrderResponse()
            : Error.Failure(description: "خطا در ثبت اطلاعات");
    }
}

public sealed class CancelOrderCommandValidator : AbstractValidator<CancelOrderCommand>
{
    public CancelOrderCommandValidator()
    {
        RuleFor(x => x.OrderId)
            .NotEmpty().WithMessage("شناسه درخواست اجباری می‌باشد.");
    }
}