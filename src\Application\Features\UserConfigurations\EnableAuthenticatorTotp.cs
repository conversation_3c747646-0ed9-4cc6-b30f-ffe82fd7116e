﻿using DispatchR.Requests.Send;
using ErrorOr;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PayPing.Auth.AccessManagement;
using System.ComponentModel.DataAnnotations;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Common.Constants;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.UserConfigurations;

public sealed record EnableAuthenticatorTotpRequest(
    [Required(AllowEmptyStrings = false, ErrorMessage = "Code is required")]
    string Code);

public sealed class EnableAuthenticatorTotpController : ApiControllerBase
{
    /// <summary>
    /// Enables user's Authenticator Totp. this is the final step for Cash out totp activation.
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("users/enable-authenticator-totp")]
    [Authorize("write")]
    [Authorize(WellKnownNames.DefaultJitAccessPolicyName)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> EnableAuthenticatorTotpAsync([FromBody] EnableAuthenticatorTotpRequest request)
    {
        var command = new EnableAuthenticatorTotpCommand(request.Code);
        var result = await Mediator.Send(command, HttpContext.RequestAborted);
        return result.Match(id => Ok(id), Problem);
    }
}

public sealed record EnableAuthenticatorTotpCommand(string Code)
    : IRequest<EnableAuthenticatorTotpCommand, Task<ErrorOr<Success>>>;

public sealed class EnableAuthenticatorTotpCommandHandler(
    ApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ITotpProvider totpProvider,
    ILogger<EnableAuthenticatorTotpCommandHandler> logger)
    : IRequestHandler<EnableAuthenticatorTotpCommand, Task<ErrorOr<Success>>>
{
    public async Task<ErrorOr<Success>> Handle(EnableAuthenticatorTotpCommand request, CancellationToken cancellationToken)
    {
        var userConfig = await dbContext.UserConfigs
            .AsTracking()
            .Where(uc => uc.UserId == currentUserService.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (string.IsNullOrWhiteSpace(userConfig?.AuthenticatorTotpSecretKey))
        {
            return Error.Forbidden(description: "کد داده شده تایید نشد");
        }

        var codeVerificationResult = totpProvider.VerifyTotpCode(userConfig.AuthenticatorTotpSecretKey, request.Code);

        if (!codeVerificationResult)
            return Error.Failure(description: "کد داده شده تایید نشد");

        if (userConfig.AuthenticatorTotpEnabled)
        {
            return Result.Success;
        }

        userConfig.AuthenticatorTotpEnabled = true;
        await dbContext.SaveChangesAsync(cancellationToken);

        logger.LogInformation(
            LogEventIds.SettlementAuthenticatorTotpEnabled,
            LogMessages.SettlementAuthenticatorTotpEnabledMessage, currentUserService.UserId);

        return Result.Success;
    }
}
