﻿using Ardalis.GuardClauses;
using ErrorOr;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Wallet;
using Wallet.Type;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Common.Interfaces.GrpcClients;

namespace Zify.Settlement.Application.Infrastructure.Clients.Grpc;

public class WalletGrpcClient(
    WalletGrpc.WalletGrpcClient walletGrpcClient,
    WalletTypeGrpc.WalletTypeGrpcClient walletTypeGrpcClient,
    IDistributedCache cache,
    IUserProfileService userService,
    ILogger<WalletGrpcClient> logger) : IWalletGrpcClient
{
    public Task<ErrorOr<CreateWalletResponse>> CreateWallet(
        int userId,
        Guid creditCode,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<ErrorOr<GetWalletDetailByIdResponse>> GetWalletDetailsById(
        Guid walletId,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<ErrorOr<GetWalletTransactionTotalAmountResponse>> GetWalletTransactionTotalAmount(
        Guid walletId,
        TransactionType transactionType,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public async Task<ErrorOr<GetWalletBalanceByIdResponse>> GetWalletBalanceById(
        Guid walletId,
        CancellationToken cancellationToken = default)
    {
        Guard.Against.NullOrEmpty(walletId, nameof(walletId));
        try
        {
            var request = new GetWalletBalanceByIdRequest
            {
                WalletId = walletId.ToString()
            };

            var response = await walletGrpcClient.GetWalletBalanceByIdAsync(request, cancellationToken: cancellationToken);
            if (response.ProblemDetail is null)
            {
                return response;
            }

            logger.LogWarning(
                "An error occurred while getting wallet balance. Error details: {ProblemDetails}",
                JsonSerializer.Serialize(response.ProblemDetail));

            return Error.Failure(nameof(GetWalletBalanceById),
                "An error occurred while getting wallet balance.");
        }
        catch (Exception e)
        {
            logger.LogError(e, "An error occurred while getting wallet balance.");
            return Error.Unexpected(nameof(GetWalletBalanceById),
                "An error occurred while getting wallet balance.");
        }
    }

    public Task<ErrorOr<BlockWalletResponse>> Freeze(
        Guid walletId,
        Guid correlationId,
        decimal amount,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<ErrorOr<UnBlockWalletResponse>> Unfreeze(
        Guid walletId,
        Guid correlationId,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<ErrorOr<UnBlockWalletResponse>> UnfreezeAndWithdraw(
        Guid walletId,
        Guid correlationId,
        decimal amount,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<ErrorOr<DepositWalletResponse>> Deposit(
        Guid walletId,
        Guid correlationId,
        decimal amount,
        string description,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<ErrorOr<WithdrawWalletResponse>> Withdraw(
        Guid walletId,
        Guid correlationId,
        decimal amount,
        string description,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }
}