﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Zify.Settlement.Application.Infrastructure.Configurations.Extensions;
public static class AddConfigurationsExtension
{
    public static IServiceCollection AddConfigurations(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<ServiceDiscoveryOptions>(configuration);
        services.Configure<RedisOptions>(configuration);
        services.Configure<ElasticsearchOptions>(configuration.GetSection(ElasticsearchOptions.SectionName));

        return services;
    }
}
