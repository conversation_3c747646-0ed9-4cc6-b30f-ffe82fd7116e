﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using Zify.Settlement.Application.Infrastructure.Web;

namespace Zify.Settlement.Application.Infrastructure.Configurations.Extensions;
public static class AddCustomSwaggerExtension
{
    public static IServiceCollection AddCustomSwagger(this IServiceCollection services)
    {
        services.AddTransient<IConfigureOptions<SwaggerGenOptions>, ConfigureSwaggerOptions>();
        services.AddSwaggerGen(options =>
        {
            // JWT Bearer Authentication
            var bearerScheme = new OpenApiSecurityScheme()
            {
                Type = SecuritySchemeType.Http,
                Name = JwtBearerDefaults.AuthenticationScheme,
                Scheme = JwtBearerDefaults.AuthenticationScheme,
                Reference = new OpenApiReference
                {
                    Id = JwtBearerDefaults.AuthenticationScheme,
                    Type = ReferenceType.SecurityScheme,
                }
            };
            options.AddSecurityDefinition(JwtBearerDefaults.AuthenticationScheme, bearerScheme);
            options.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                { bearerScheme, [] }
            });

            // Handle conflicting actions (important for API versioning)
            options.ResolveConflictingActions(apiDescriptions => apiDescriptions.First());
            options.EnableAnnotations();

            // Include XML comments if available
            var apiXmlFile = Path.Combine(AppContext.BaseDirectory, "Zify.Settlement.Api.xml");
            if (File.Exists(apiXmlFile))
            {
                options.IncludeXmlComments(apiXmlFile, true);
            }

            var applicationXmlFile = Path.Combine(AppContext.BaseDirectory, "Zify.Settlement.Application.xml");
            if (File.Exists(applicationXmlFile))
            {
                options.IncludeXmlComments(applicationXmlFile, true);
            }
        });

        return services;
    }
}
