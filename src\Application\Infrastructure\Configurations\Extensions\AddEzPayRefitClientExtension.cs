﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Refit;
using System.Net;
using Zify.Settlement.Application.Infrastructure.Clients.ExternalHttp;

namespace Zify.Settlement.Application.Infrastructure.Configurations.Extensions;
public static class AddEzPayRefitClientExtension
{
    public static IServiceCollection AddEzPayRefitClient(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddRefitClient<IEzPayHttpClient>()
            .ConfigureHttpClient(c =>
            {
                c.BaseAddress = new Uri(Environment.GetEnvironmentVariable("ExPayBaseHost")!);
                c.DefaultRequestHeaders.Add("Token", Environment.GetEnvironmentVariable("EzPayToken"));
            })
            .ConfigurePrimaryHttpMessageHandler(() =>
            {
                var toReturn = new HttpClientHandler();

                if (string.IsNullOrWhiteSpace(configuration["UseProxyForEzPay"]) ||
                    configuration["UseProxyForEzPay"] != "true") return toReturn;

                toReturn.UseProxy = true;
                toReturn.Proxy = new WebProxy
                {
                    Address = new Uri(configuration["Proxy_Host"]!),
                    Credentials = new NetworkCredential(configuration["Proxy_UserName"], configuration["Proxy_Password"]),
                    BypassProxyOnLocal = true,
                };

                return toReturn;
            });

        return services;
    }
}
