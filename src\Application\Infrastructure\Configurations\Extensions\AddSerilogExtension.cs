using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog;
using Serilog.Events;
using Serilog.Exceptions;
using Serilog.Filters;
using Zify.Settlement.Application.Infrastructure.Logging;

namespace Zify.Settlement.Application.Infrastructure.Configurations.Extensions;

/// <summary>
/// Extension methods for configuring Serilog with Elasticsearch and dynamic sink selection.
/// </summary>
public static class AddSerilogExtension
{
    public static void UseCustomSerilog(this WebApplicationBuilder builder)
    {
        builder.Logging.ClearProviders();
        builder.Host.UseSerilog();

        // Add Serilog logging with Elasticsearch integration
        builder.Services.AddSerilogLogging(builder.Configuration, builder.Environment);
    }

    /// <summary>
    /// Adds Serilog with Elasticsearch integration and dynamic sink selection to the service collection.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="configuration">The configuration.</param>
    /// <param name="hostEnvironment">The host environment.</param>
    /// <returns>The service collection for chaining.</returns>
    private static void AddSerilogLogging(this IServiceCollection services,
        ConfigurationManager configuration,
        IHostEnvironment hostEnvironment)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configuration);
        ArgumentNullException.ThrowIfNull(hostEnvironment);

        // Configure Elasticsearch options with validation
        services.AddSingleton<IValidateOptions<ElasticsearchOptions>, ElasticsearchOptionsValidator>();

        // Register sink selection strategies
        services.AddSingleton<ISinkSelectionStrategy, TcpSinkStrategy>();
        services.AddSingleton<ISinkSelectionStrategy, UdpSinkStrategy>();
        services.AddSingleton<SinkSelectionFactory>();

        // Configure Serilog
        services.AddSerilog((serviceProvider, loggerConfiguration) =>
        {
            ConfigureSerilog(loggerConfiguration, serviceProvider, hostEnvironment);
        });
    }

    /// <summary>
    /// Adds Serilog with custom configuration action.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="configureOptions">Action to configure Elasticsearch options.</param>
    /// <param name="hostEnvironment">The host environment.</param>
    /// <returns>The service collection for chaining.</returns>
    public static IServiceCollection AddSerilogLogging(
        this IServiceCollection services,
        Action<ElasticsearchOptions> configureOptions,
        IHostEnvironment hostEnvironment)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configureOptions);
        ArgumentNullException.ThrowIfNull(hostEnvironment);

        // Configure options using action
        services.Configure(configureOptions);
        services.AddSingleton<IValidateOptions<ElasticsearchOptions>, ElasticsearchOptionsValidator>();

        // Register sink selection strategies
        services.AddSingleton<ISinkSelectionStrategy, TcpSinkStrategy>();
        services.AddSingleton<ISinkSelectionStrategy, UdpSinkStrategy>();
        services.AddSingleton<SinkSelectionFactory>();

        // Configure Serilog
        services.AddSerilog((serviceProvider, loggerConfiguration) =>
        {
            ConfigureSerilog(loggerConfiguration, serviceProvider, hostEnvironment);
        });

        return services;
    }

    private static void ConfigureSerilog(
        LoggerConfiguration loggerConfiguration,
        IServiceProvider serviceProvider,
        IHostEnvironment hostEnvironment)
    {
        // Get Elasticsearch options
        var elasticsearchOptions = serviceProvider.GetRequiredService<IOptions<ElasticsearchOptions>>().Value;

        // Get sink selection factory
        var sinkFactory = serviceProvider.GetRequiredService<SinkSelectionFactory>();

        // Configure base logger settings
        loggerConfiguration
            .MinimumLevel.Information()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
            .MinimumLevel.Override("Microsoft.Hosting.Lifetime", LogEventLevel.Information)
            .MinimumLevel.Override("Microsoft.EntityFrameworkCore", LogEventLevel.Warning)
            .MinimumLevel.Override("System", LogEventLevel.Warning)
            .MinimumLevel.Override("Microsoft.AspNetCore.Authentication", LogEventLevel.Information)
            .Enrich.FromLogContext()
            .Enrich.WithExceptionDetails()
            .Enrich.WithProcessId()
            .Enrich.WithProcessName()
            .Enrich.WithProperty("Application", "PayPing.Settlement")
            .Enrich.WithProperty("Environment", hostEnvironment.EnvironmentName)
            .Filter.ByExcluding(Matching.FromSource("Microsoft.AspNetCore.StaticFiles"))
            .Filter.ByExcluding(Matching.FromSource("Microsoft.AspNetCore.Routing.EndpointMiddleware"));

        // Configure console sink for development
        if (hostEnvironment.IsDevelopment())
        {
            loggerConfiguration.WriteTo.Console(
                //outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"
                );
        }

        try
        {
            // Get and apply the appropriate sink strategy
            var strategy = sinkFactory.GetStrategy(elasticsearchOptions);
            loggerConfiguration = strategy.ConfigureSinks(loggerConfiguration, elasticsearchOptions);
        }
        catch (Exception ex)
        {
            // Fallback to console logging if sink configuration fails
            Console.WriteLine($"Failed to configure logging sinks: {ex.Message}");
            loggerConfiguration.WriteTo.Console();
        }
    }
}
