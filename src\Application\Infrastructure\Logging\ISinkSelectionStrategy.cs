using Serilog;
using Zify.Settlement.Application.Infrastructure.Configurations;

namespace Zify.Settlement.Application.Infrastructure.Logging;

/// <summary>
/// Defines a strategy for selecting and configuring Serilog sinks based on configuration.
/// </summary>
public interface ISinkSelectionStrategy
{
    /// <summary>
    /// Configures the logger with the appropriate sinks based on the strategy.
    /// </summary>
    /// <param name="loggerConfiguration">The logger configuration to modify.</param>
    /// <param name="options">The Elasticsearch configuration options.</param>
    /// <returns>The modified logger configuration.</returns>
    LoggerConfiguration ConfigureSinks(LoggerConfiguration loggerConfiguration, ElasticsearchOptions options);

    /// <summary>
    /// Gets whether this strategy can handle the given configuration.
    /// </summary>
    /// <param name="options">The Elasticsearch configuration options.</param>
    /// <returns>True if this strategy can handle the configuration, false otherwise.</returns>
    bool CanHandle(ElasticsearchOptions options);
}
