using Zify.Settlement.Application.Infrastructure.Configurations;

namespace Zify.Settlement.Application.Infrastructure.Logging;

/// <summary>
/// Factory for creating sink selection strategies based on configuration.
/// </summary>
public sealed class SinkSelectionFactory
{
    private readonly IEnumerable<ISinkSelectionStrategy> _strategies;

    public SinkSelectionFactory(IEnumerable<ISinkSelectionStrategy> strategies)
    {
        _strategies = strategies ?? throw new ArgumentNullException(nameof(strategies));
    }

    /// <summary>
    /// Gets the appropriate sink selection strategy based on the configuration.
    /// </summary>
    /// <param name="options">The Elasticsearch configuration options.</param>
    /// <returns>The appropriate sink selection strategy.</returns>
    /// <exception cref="InvalidOperationException">Thrown when no suitable strategy is found.</exception>
    public ISinkSelectionStrategy GetStrategy(ElasticsearchOptions options)
    {
        ArgumentNullException.ThrowIfNull(options);

        var strategy = _strategies.FirstOrDefault(s => s.<PERSON>le(options));
        
        if (strategy == null)
        {
            throw new InvalidOperationException(
                $"No sink selection strategy found for Logstash type: {options.LogstashType}. " +
                "Supported types are: TCP, UDP");
        }

        return strategy;
    }

    /// <summary>
    /// Gets all available strategies.
    /// </summary>
    /// <returns>All available sink selection strategies.</returns>
    public IEnumerable<ISinkSelectionStrategy> GetAllStrategies()
    {
        return _strategies;
    }
}
