using Elastic.CommonSchema.Serilog;
using Serilog;
using Serilog.Events;
using Serilog.Sinks.Network;
using Zify.Settlement.Application.Infrastructure.Configurations;

namespace Zify.Settlement.Application.Infrastructure.Logging;

/// <summary>
/// Strategy for configuring TCP-based logging sinks.
/// </summary>
public sealed class TcpSinkStrategy : ISinkSelectionStrategy
{
    /// <inheritdoc />
    public LoggerConfiguration ConfigureSinks(LoggerConfiguration loggerConfiguration, ElasticsearchOptions options)
    {
        ArgumentNullException.ThrowIfNull(loggerConfiguration);
        ArgumentNullException.ThrowIfNull(options);

        // Configure Elasticsearch sink if enabled
        if (options.ElasticsearchEnabled)
        {
            loggerConfiguration = ConfigureElasticsearchSink(loggerConfiguration, options);
        }

        // Configure TCP Logstash sink if enabled
        if (options.LogstashEnabled)
        {
            loggerConfiguration = ConfigureTcpLogstashSink(loggerConfiguration, options);
        }

        return loggerConfiguration;
    }

    /// <inheritdoc />
    public bool CanHandle(ElasticsearchOptions options)
    {
        return options.LogstashType.Equals("TCP", StringComparison.OrdinalIgnoreCase);
    }

    private static LoggerConfiguration ConfigureElasticsearchSink(LoggerConfiguration loggerConfiguration, ElasticsearchOptions options)
    {
        var elasticsearchSinkOptions = new Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions(new Uri(options.Uri))
        {
            IndexFormat = $"{options.IndexName}-{{0:yyyy.MM.dd}}",
            AutoRegisterTemplate = options.AutoRegisterTemplate,
            NumberOfShards = options.NumberOfShards,
            NumberOfReplicas = options.NumberOfReplicas,
            BatchPostingLimit = options.BatchPostingLimit,
            Period = options.Period,
            ConnectionTimeout = TimeSpan.FromMilliseconds(options.ConnectionTimeout),
            DeadLetterIndexName = $"{options.IndexName}-deadletter"
        };

        // Configure authentication if provided
        if (!string.IsNullOrWhiteSpace(options.Username) && !string.IsNullOrWhiteSpace(options.Password))
        {
            elasticsearchSinkOptions.ModifyConnectionSettings = connectionConfiguration =>
                connectionConfiguration.BasicAuthentication(options.Username, options.Password);
        }

        // Add Elasticsearch sink
        return loggerConfiguration.WriteTo.Elasticsearch(elasticsearchSinkOptions);
    }

    private static LoggerConfiguration ConfigureTcpLogstashSink(LoggerConfiguration loggerConfiguration, ElasticsearchOptions options)
    {
        //var formatter = new JsonFormatter(renderMessage: true);

        // Parse minimum level
        if (Enum.TryParse<LogEventLevel>(options.MinimumLevel, true, out var minLevel))
        {
            return loggerConfiguration.WriteTo.TCPSink(
                options.LogstashEndpoint,
                new EcsTextFormatter(),
                restrictedToMinimumLevel: minLevel);
        }

        return loggerConfiguration.WriteTo.TCPSink(
            options.LogstashEndpoint,
            new EcsTextFormatter());
    }
}
