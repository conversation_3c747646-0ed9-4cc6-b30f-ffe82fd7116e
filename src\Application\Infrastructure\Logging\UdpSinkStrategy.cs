using Elastic.CommonSchema.Serilog;
using Serilog;
using Serilog.Events;
using Serilog.Sinks.Network;
using System.Net;
using Zify.Settlement.Application.Infrastructure.Configurations;

namespace Zify.Settlement.Application.Infrastructure.Logging;

/// <summary>
/// Strategy for configuring UDP-based logging sinks.
/// </summary>
public sealed class UdpSinkStrategy : ISinkSelectionStrategy
{
    /// <inheritdoc />
    public LoggerConfiguration ConfigureSinks(LoggerConfiguration loggerConfiguration, ElasticsearchOptions options)
    {
        ArgumentNullException.ThrowIfNull(loggerConfiguration);
        ArgumentNullException.ThrowIfNull(options);

        // Configure Elasticsearch sink if enabled
        if (options.ElasticsearchEnabled)
        {
            loggerConfiguration = ConfigureElasticsearchSink(loggerConfiguration, options);
        }

        // Configure UDP Logstash sink if enabled
        if (options.LogstashEnabled)
        {
            loggerConfiguration = ConfigureUdpLogstashSink(loggerConfiguration, options);
        }

        return loggerConfiguration;
    }

    /// <inheritdoc />
    public bool CanHandle(ElasticsearchOptions options)
    {
        return options.LogstashType.Equals("UDP", StringComparison.OrdinalIgnoreCase);
    }

    private static LoggerConfiguration ConfigureElasticsearchSink(LoggerConfiguration loggerConfiguration, ElasticsearchOptions options)
    {
        var elasticsearchSinkOptions = new Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions(new Uri(options.Uri))
        {
            IndexFormat = $"{options.IndexName}-{{0:yyyy.MM.dd}}",
            AutoRegisterTemplate = options.AutoRegisterTemplate,
            NumberOfShards = options.NumberOfShards,
            NumberOfReplicas = options.NumberOfReplicas,
            BatchPostingLimit = options.BatchPostingLimit,
            Period = options.Period,
            ConnectionTimeout = TimeSpan.FromMilliseconds(options.ConnectionTimeout),
            DeadLetterIndexName = $"{options.IndexName}-deadletter"
        };

        // Configure authentication if provided
        if (!string.IsNullOrWhiteSpace(options.Username) && !string.IsNullOrWhiteSpace(options.Password))
        {
            elasticsearchSinkOptions.ModifyConnectionSettings = connectionConfiguration =>
                connectionConfiguration.BasicAuthentication(options.Username, options.Password);
        }

        // Add Elasticsearch sink
        return loggerConfiguration.WriteTo.Elasticsearch(elasticsearchSinkOptions);
    }

    private static LoggerConfiguration ConfigureUdpLogstashSink(LoggerConfiguration loggerConfiguration, ElasticsearchOptions options)
    {
        //var formatter = new JsonFormatter(renderMessage: true);

        // Try to parse the address as an IP address, otherwise use it as hostname
        if (IPAddress.TryParse(options.LogstashAddress, out var ipAddress))
        {
            // Parse minimum level
            if (Enum.TryParse<LogEventLevel>(options.MinimumLevel, true, out var minLevel))
            {
                return loggerConfiguration.WriteTo.UDPSink(
                    ipAddress,
                    options.LogstashPort,
                    new EcsTextFormatter(),
                    restrictedToMinimumLevel: minLevel);
            }

            return loggerConfiguration.WriteTo.UDPSink(
                ipAddress,
                options.LogstashPort,
                new EcsTextFormatter());
        }
        else
        {
            // Use hostname version
            // Parse minimum level
            if (Enum.TryParse<LogEventLevel>(options.MinimumLevel, true, out var minLevel))
            {
                return loggerConfiguration.WriteTo.UDPSink(
                    options.LogstashAddress,
                    options.LogstashPort,
                    new EcsTextFormatter(),
                    restrictedToMinimumLevel: minLevel);
            }

            return loggerConfiguration.WriteTo.UDPSink(
                options.LogstashAddress,
                options.LogstashPort,
                new EcsTextFormatter());
        }
    }
}
