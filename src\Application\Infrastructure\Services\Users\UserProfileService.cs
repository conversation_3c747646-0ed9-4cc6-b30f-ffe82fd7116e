﻿using ErrorOr;
using Zify.Settlement.Application.Common.Interfaces;
using Zify.Settlement.Application.Infrastructure.Services.Users.Models;

namespace Zify.Settlement.Application.Infrastructure.Services.Users;

public class UserProfileService(PayPing.User.AdminSDK.Services.Definition.IUserManagerService userManagerService)
    : IUserProfileService
{
    public async Task<ErrorOr<FindUserByIdResponse>> FindUserByIdAsync(int userId, CancellationToken cancellationToken = default)
    {
        var user =
            await userManagerService.FindUserByIdAsync(userId, cancellationToken);

        if (user.Succeeded)
        {
            return new FindUserByIdResponse(
                Id: user.SuccessResult.Id,
                PhoneNumberConfirmed: user.SuccessResult.PhoneNumberConfirmed,
                PhoneNumber: user.SuccessResult.PhoneNumber,
                Email: user.SuccessResult.Email,
                EmailConfirmed: user.SuccessResult.EmailConfirmed,
                FirstName: user.SuccessResult.FirstName,
                LastName: user.SuccessResult.LastName,
                FullName: user.SuccessResult.FullName,
                UserName: user.SuccessResult.UserName,
                ProfilePicture: user.SuccessResult.ProfilePicture,
                BirthDate: user.SuccessResult.BirthDate,
                CreatedDateTime: user.SuccessResult.CreatedDateTime,
                IsActive: user.SuccessResult.IsActive,
                LockoutEnd: user.SuccessResult.LockoutEnd,
                LockoutEnabled: user.SuccessResult.LockoutEnabled,
                BusinessName: user.SuccessResult.BusinessName
            );
        }

        return Error.Failure(user.Message);
    }


    public async Task<ErrorOr<List<GetUserFullNameResponse>>> GetUserFullNameAsync(List<int> userIds, CancellationToken cancellationToken = default)
    {
        var users = await userManagerService.GetFullNameAsync(userIds, cancellationToken);

        if (users.Succeeded)
        {
            return users.SuccessResult
                .ConvertAll(user => new GetUserFullNameResponse(user.UserId, user.FullName));
        }

        return Error.Failure(users.Message);
    }
}