﻿using DecimalGrpc;

namespace Zify.Settlement.Application.Infrastructure.Utilities;

public static class CurrencyHelpers
{
    public static decimal ToRial(this decimal toman)
    {
        return decimal.Multiply(toman, 10m);
    }

    public static decimal ToToman(this decimal rial)
    {
        return Math.Floor(decimal.Divide(rial, 10m));
    }

    public static int ToRial(this int toman)
    {
        return toman * 10;
    }

    public static int ToToman(this int rial)
    {
        return (int)Math.Floor(decimal.Divide(rial, 10m));
    }

    public static DecimalValue ToWalletDecimalValue(this decimal value)
    {
        var units = (long)value;
        var fractionalPart = value - units;

        // Ensure maximum 9 decimal places
        var nanos = (int)Math.Round(fractionalPart * 1_000_000_000M);
        return new DecimalValue
        {
            Units = units,
            Nanos = nanos
        };
    }

    public static decimal ToDecimal(this DecimalValue value) => value.Units + value.Nanos / 1_000_000_000M;
}