﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<IsPackable>false</IsPackable>
		<IsTestProject>true</IsTestProject>
		<AssemblyName>PayPing.Settlement.$(MSBuildProjectName)</AssemblyName>
		<RootNamespace>$(AssemblyName)</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.6" />
		<PackageReference Include="coverlet.collector" Version="6.0.4">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
		<PackageReference Include="Shouldly" Version="4.3.0" />
		<PackageReference Include="System.Text.Encodings.Web" Version="9.0.6" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.1.1">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.6" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\src\Api\Api.csproj" />
	  <ProjectReference Include="..\..\src\Application\Application.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Using Include="Xunit" />
	</ItemGroup>

</Project>
