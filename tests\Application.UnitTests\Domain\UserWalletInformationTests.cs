using Shouldly;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace PayPing.Settlement.Application.UnitTests.Domain;

public class UserWalletInformationTests
{
    [Fact]
    public void Create_ShouldCreateUserWalletInformation_WhenValidParametersProvided()
    {
        // Arrange
        var userId = 123;
        var paymentWalletId = Guid.NewGuid();

        // Act
        var userWalletInfo = UserWalletInformation.Create(userId, paymentWalletId);

        // Assert
        userWalletInfo.ShouldNotBeNull();
        userWalletInfo.UserId.ShouldBe(userId);
        userWalletInfo.PaymentWalletId.Value.ShouldBe(paymentWalletId);
        userWalletInfo.SettlementWalletId.ShouldBeNull();
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(-100)]
    public void Create_ShouldThrowArgumentException_WhenUserIdIsZeroOrNegative(int userId)
    {
        // Arrange
        var paymentWalletId = Guid.NewGuid();

        // Act & Assert
        var exception = Should.Throw<ArgumentException>(() =>
            UserWalletInformation.Create(userId, paymentWalletId));

        exception.ParamName.ShouldBe("userId");
    }

    [Fact]
    public void Create_ShouldThrowArgumentException_WhenPaymentWalletIdIsEmpty()
    {
        // Arrange
        var userId = 123;
        var emptyGuid = Guid.Empty;

        // Act & Assert
        var exception = Should.Throw<ArgumentException>(() =>
            UserWalletInformation.Create(userId, emptyGuid));

        exception.ParamName.ShouldBe("paymentWalletId");
    }

    [Fact]
    public void UpdatePaymentWalletId_ShouldUpdateWalletId_WhenValidGuidProvided()
    {
        // Arrange
        var userId = 456;
        var originalWalletId = Guid.NewGuid();
        var newWalletId = Guid.NewGuid();

        var userWalletInfo = UserWalletInformation.Create(userId, originalWalletId);

        // Act
        userWalletInfo.UpdatePaymentWalletId(newWalletId);

        // Assert
        userWalletInfo.PaymentWalletId.Value.ShouldBe(newWalletId);
        userWalletInfo.PaymentWalletId.Value.ShouldNotBe(originalWalletId);
    }

    [Fact]
    public void UpdatePaymentWalletId_ShouldThrowArgumentException_WhenEmptyGuidProvided()
    {
        // Arrange
        var userId = 789;
        var originalWalletId = Guid.NewGuid();
        var userWalletInfo = UserWalletInformation.Create(userId, originalWalletId);

        // Act & Assert
        var exception = Should.Throw<ArgumentException>(() =>
            userWalletInfo.UpdatePaymentWalletId(Guid.Empty));

        exception.ParamName.ShouldBe("paymentWalletId");
    }

    [Fact]
    public void SetSettlementWalletId_ShouldSetWalletId_WhenValidGuidProvided()
    {
        // Arrange
        var userId = 101;
        var paymentWalletId = Guid.NewGuid();
        var settlementWalletId = Guid.NewGuid();

        var userWalletInfo = UserWalletInformation.Create(userId, paymentWalletId);

        // Act
        userWalletInfo.SetSettlementWalletId(settlementWalletId);

        // Assert
        userWalletInfo.SettlementWalletId.ShouldNotBeNull();
        userWalletInfo.SettlementWalletId?.Value.ShouldBe(settlementWalletId);
    }

    [Fact]
    public void SetSettlementWalletId_ShouldThrowArgumentException_WhenEmptyGuidProvided()
    {
        // Arrange
        var userId = 202;
        var paymentWalletId = Guid.NewGuid();
        var userWalletInfo = UserWalletInformation.Create(userId, paymentWalletId);

        // Act & Assert
        var exception = Should.Throw<ArgumentException>(() =>
            userWalletInfo.SetSettlementWalletId(Guid.Empty));

        exception.ParamName.ShouldBe("settlementWalletId");
    }

    [Fact]
    public void Create_ShouldCreateWalletIdValueObject_WhenValidGuidProvided()
    {
        // Arrange
        var userId = 303;
        var paymentWalletId = Guid.NewGuid();

        // Act
        var userWalletInfo = UserWalletInformation.Create(userId, paymentWalletId);

        // Assert
        userWalletInfo.PaymentWalletId.ShouldBeOfType<WalletId>();
        userWalletInfo.PaymentWalletId.Value.ShouldBe(paymentWalletId);
    }

    [Fact]
    public void UpdatePaymentWalletId_ShouldCreateNewWalletIdValueObject_WhenCalled()
    {
        // Arrange
        var userId = 404;
        var originalWalletId = Guid.NewGuid();
        var newWalletId = Guid.NewGuid();

        var userWalletInfo = UserWalletInformation.Create(userId, originalWalletId);
        var originalWalletIdObject = userWalletInfo.PaymentWalletId;

        // Act
        userWalletInfo.UpdatePaymentWalletId(newWalletId);

        // Assert
        userWalletInfo.PaymentWalletId.ShouldNotBe(originalWalletIdObject);
        userWalletInfo.PaymentWalletId.Value.ShouldBe(newWalletId);
    }

    [Fact]
    public void SetSettlementWalletId_ShouldCreateWalletIdValueObject_WhenValidGuidProvided()
    {
        // Arrange
        var userId = 505;
        var paymentWalletId = Guid.NewGuid();
        var settlementWalletId = Guid.NewGuid();

        var userWalletInfo = UserWalletInformation.Create(userId, paymentWalletId);

        // Act
        userWalletInfo.SetSettlementWalletId(settlementWalletId);

        // Assert
        userWalletInfo.SettlementWalletId.ShouldNotBeNull();
        userWalletInfo.SettlementWalletId.ShouldBeOfType<WalletId>();
        userWalletInfo.SettlementWalletId?.Value.ShouldBe(settlementWalletId);
    }

    [Theory]
    [InlineData(1)]
    [InlineData(100)]
    [InlineData(999999)]
    [InlineData(int.MaxValue)]
    public void Create_ShouldAcceptAnyPositiveUserId_WhenValidGuidProvided(int userId)
    {
        // Arrange
        var paymentWalletId = Guid.NewGuid();

        // Act
        var userWalletInfo = UserWalletInformation.Create(userId, paymentWalletId);

        // Assert
        userWalletInfo.UserId.ShouldBe(userId);
        userWalletInfo.PaymentWalletId.Value.ShouldBe(paymentWalletId);
    }

    [Fact]
    public void UpdatePaymentWalletId_ShouldAllowMultipleUpdates_WhenValidGuidsProvided()
    {
        // Arrange
        var userId = 606;
        var originalWalletId = Guid.NewGuid();
        var firstUpdateId = Guid.NewGuid();
        var secondUpdateId = Guid.NewGuid();

        var userWalletInfo = UserWalletInformation.Create(userId, originalWalletId);

        // Act
        userWalletInfo.UpdatePaymentWalletId(firstUpdateId);
        userWalletInfo.UpdatePaymentWalletId(secondUpdateId);

        // Assert
        userWalletInfo.PaymentWalletId.Value.ShouldBe(secondUpdateId);
        userWalletInfo.PaymentWalletId.Value.ShouldNotBe(firstUpdateId);
        userWalletInfo.PaymentWalletId.Value.ShouldNotBe(originalWalletId);
    }

    [Fact]
    public void SetSettlementWalletId_ShouldAllowMultipleUpdates_WhenValidGuidsProvided()
    {
        // Arrange
        var userId = 707;
        var paymentWalletId = Guid.NewGuid();
        var firstSettlementId = Guid.NewGuid();
        var secondSettlementId = Guid.NewGuid();

        var userWalletInfo = UserWalletInformation.Create(userId, paymentWalletId);

        // Act
        userWalletInfo.SetSettlementWalletId(firstSettlementId);
        userWalletInfo.SetSettlementWalletId(secondSettlementId);

        // Assert
        userWalletInfo.SettlementWalletId?.Value.ShouldBe(secondSettlementId);
        userWalletInfo.SettlementWalletId?.Value.ShouldNotBe(firstSettlementId);
    }

    [Fact]
    public void Create_ShouldNotAffectSettlementWalletId_WhenCreatingNewInstance()
    {
        // Arrange
        var userId = 808;
        var paymentWalletId = Guid.NewGuid();

        // Act
        var userWalletInfo = UserWalletInformation.Create(userId, paymentWalletId);

        // Assert
        userWalletInfo.SettlementWalletId.ShouldBeNull();
    }
}
