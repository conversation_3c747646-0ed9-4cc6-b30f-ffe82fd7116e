using Shouldly;
using Zify.Settlement.Application.Domain.Exceptions;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace PayPing.Settlement.Application.UnitTests.Domain.ValueObjects;

public class WalletIdTests
{
    [Fact]
    public void New_ShouldCreateNewWalletId_WhenCalled()
    {
        // Act
        var walletId = WalletId.New();

        // Assert
        walletId.Value.ShouldNotBe(Guid.Empty);
    }

    [Fact]
    public void Of_ShouldCreateWalletId_WhenValidGuidProvided()
    {
        // Arrange
        var guid = Guid.NewGuid();

        // Act
        var walletId = WalletId.Of(guid);

        // Assert
        walletId.Value.ShouldBe(guid);
    }

    [Fact]
    public void Of_ShouldThrowInvalidWalletIdException_WhenEmptyGuidProvided()
    {
        // Arrange
        var emptyGuid = Guid.Empty;

        // Act & Assert
        var exception = Should.Throw<InvalidWalletIdException>(() => WalletId.Of(emptyGuid));
        exception.WalletId.ShouldBe(emptyGuid);
    }

    [Fact]
    public void Parse_ShouldCreateWalletId_WhenValidGuidStringProvided()
    {
        // Arrange
        var guid = Guid.NewGuid();
        var guidString = guid.ToString();

        // Act
        var walletId = WalletId.Parse(guidString);

        // Assert
        walletId.Value.ShouldBe(guid);
    }

    [Theory]
    [InlineData("invalid-guid")]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("not-a-guid-at-all")]
    [InlineData("123")]
    public void Parse_ShouldThrowInvalidWalletIdException_WhenInvalidStringProvided(string invalidGuidString)
    {
        // Act & Assert
        Should.Throw<InvalidWalletIdException>(() => WalletId.Parse(invalidGuidString));
    }

    [Fact]
    public void Parse_ShouldThrowInvalidWalletIdException_WhenEmptyGuidStringProvided()
    {
        // Arrange
        var emptyGuidString = Guid.Empty.ToString();

        // Act & Assert
        Should.Throw<InvalidWalletIdException>(() => WalletId.Parse(emptyGuidString));
    }

    [Fact]
    public void Parse_ShouldThrowInvalidWalletIdException_WhenNullStringProvided()
    {
        // Act & Assert
        Should.Throw<InvalidWalletIdException>(() => WalletId.Parse(null!));
    }

    [Fact]
    public void TryParse_ShouldReturnTrue_WhenValidGuidStringProvided()
    {
        // Arrange
        var guid = Guid.NewGuid();
        var guidString = guid.ToString();

        // Act
        var result = WalletId.TryParse(guidString, out var walletId);

        // Assert
        result.ShouldBeTrue();
        walletId.Value.ShouldBe(guid);
    }

    [Theory]
    [InlineData("invalid-guid")]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData("not-a-guid-at-all")]
    [InlineData("123")]
    public void TryParse_ShouldReturnFalse_WhenInvalidStringProvided(string invalidGuidString)
    {
        // Act
        var result = WalletId.TryParse(invalidGuidString, out var walletId);

        // Assert
        result.ShouldBeFalse();
        walletId.ShouldBe(default(WalletId));
    }

    [Fact]
    public void TryParse_ShouldReturnFalse_WhenEmptyGuidStringProvided()
    {
        // Arrange
        var emptyGuidString = Guid.Empty.ToString();

        // Act
        var result = WalletId.TryParse(emptyGuidString, out var walletId);

        // Assert
        result.ShouldBeFalse();
        walletId.ShouldBe(default(WalletId));
    }

    [Fact]
    public void TryParse_ShouldReturnFalse_WhenNullStringProvided()
    {
        // Act
        var result = WalletId.TryParse(null!, out var walletId);

        // Assert
        result.ShouldBeFalse();
        walletId.ShouldBe(default(WalletId));
    }

    [Fact]
    public void ToString_ShouldReturnGuidString_WhenCalled()
    {
        // Arrange
        var guid = Guid.NewGuid();
        var walletId = WalletId.Of(guid);

        // Act
        var result = walletId.ToString();

        // Assert
        result.ShouldBe(guid.ToString());
    }

    [Fact]
    public void ImplicitOperator_ShouldConvertToGuid_WhenUsed()
    {
        // Arrange
        var guid = Guid.NewGuid();
        var walletId = WalletId.Of(guid);

        // Act
        Guid convertedGuid = walletId;

        // Assert
        convertedGuid.ShouldBe(guid);
    }

    [Fact]
    public void Equals_ShouldReturnTrue_WhenSameGuidValues()
    {
        // Arrange
        var guid = Guid.NewGuid();
        var walletId1 = WalletId.Of(guid);
        var walletId2 = WalletId.Of(guid);

        // Act & Assert
        walletId1.ShouldBe(walletId2);
        walletId1.Equals(walletId2).ShouldBeTrue();
    }

    [Fact]
    public void Equals_ShouldReturnFalse_WhenDifferentGuidValues()
    {
        // Arrange
        var guid1 = Guid.NewGuid();
        var guid2 = Guid.NewGuid();
        var walletId1 = WalletId.Of(guid1);
        var walletId2 = WalletId.Of(guid2);

        // Act & Assert
        walletId1.ShouldNotBe(walletId2);
        walletId1.Equals(walletId2).ShouldBeFalse();
    }

    [Fact]
    public void GetHashCode_ShouldReturnSameValue_WhenSameGuidValues()
    {
        // Arrange
        var guid = Guid.NewGuid();
        var walletId1 = WalletId.Of(guid);
        var walletId2 = WalletId.Of(guid);

        // Act & Assert
        walletId1.GetHashCode().ShouldBe(walletId2.GetHashCode());
    }

    [Fact]
    public void GetHashCode_ShouldReturnDifferentValues_WhenDifferentGuidValues()
    {
        // Arrange
        var guid1 = Guid.NewGuid();
        var guid2 = Guid.NewGuid();
        var walletId1 = WalletId.Of(guid1);
        var walletId2 = WalletId.Of(guid2);

        // Act & Assert
        walletId1.GetHashCode().ShouldNotBe(walletId2.GetHashCode());
    }

    [Theory]
    [InlineData("123e4567-e89b-12d3-a456-************")]
    [InlineData("A1B2C3D4-E5F6-7890-ABCD-EF1234567890")]
    [InlineData("12345678-1234-1234-1234-123456789012")]
    public void Parse_ShouldAcceptValidGuidFormats_WhenValidFormatsProvided(string validGuidString)
    {
        // Act
        var walletId = WalletId.Parse(validGuidString);

        // Assert
        walletId.Value.ShouldBe(Guid.Parse(validGuidString));
    }

    [Theory]
    [InlineData("123e4567-e89b-12d3-a456-************")]
    [InlineData("A1B2C3D4-E5F6-7890-ABCD-EF1234567890")]
    [InlineData("12345678-1234-1234-1234-123456789012")]
    public void TryParse_ShouldAcceptValidGuidFormats_WhenValidFormatsProvided(string validGuidString)
    {
        // Act
        var result = WalletId.TryParse(validGuidString, out var walletId);

        // Assert
        result.ShouldBeTrue();
        walletId.Value.ShouldBe(Guid.Parse(validGuidString));
    }

    [Fact]
    public void New_ShouldCreateUniqueWalletIds_WhenCalledMultipleTimes()
    {
        // Act
        var walletId1 = WalletId.New();
        var walletId2 = WalletId.New();
        var walletId3 = WalletId.New();

        // Assert
        walletId1.ShouldNotBe(walletId2);
        walletId1.ShouldNotBe(walletId3);
        walletId2.ShouldNotBe(walletId3);
    }

    [Fact]
    public void Value_ShouldBeReadOnly_WhenAccessed()
    {
        // Arrange
        var guid = Guid.NewGuid();
        var walletId = WalletId.Of(guid);

        // Act & Assert
        walletId.Value.ShouldBe(guid);
        
        // Verify that Value property doesn't have a setter (compile-time check)
        var valueProperty = typeof(WalletId).GetProperty(nameof(WalletId.Value));
        valueProperty.ShouldNotBeNull();
        valueProperty.CanWrite.ShouldBeFalse();
    }
}
