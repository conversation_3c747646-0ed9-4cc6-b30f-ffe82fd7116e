using Microsoft.EntityFrameworkCore;
using Shouldly;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.Exceptions;
using Zify.Settlement.Application.Features.UserConfigurations;

namespace PayPing.Settlement.Application.UnitTests.Features.Users;

public class AddUserPaymentWalletCommandHandlerTests : TempDatabase
{
    private readonly AddUserPaymentWalletCommandHandler _handler;

    public AddUserPaymentWalletCommandHandlerTests()
    {
        _handler = new AddUserPaymentWalletCommandHandler(Context);
    }

    [Fact]
    public async Task Handle_ShouldCreateNewUserConfigWithWalletInformation_WhenUserConfigDoesNotExist()
    {
        // Arrange
        var userId = 123;
        var paymentWalletId = Guid.NewGuid().ToString();
        var command = new AddUserPaymentWalletCommand(userId, paymentWalletId);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        var userConfig = await Context.UserConfigs
            .Include(x => x.WalletInformation)
            .FirstOrDefaultAsync(x => x.UserId == userId);

        userConfig.ShouldNotBeNull();
        userConfig.UserId.ShouldBe(userId);
        userConfig.WalletInformation.ShouldNotBeNull();
        userConfig.WalletInformation.UserId.ShouldBe(userId);
        userConfig.WalletInformation.PaymentWalletId.Value.ShouldBe(Guid.Parse(paymentWalletId));
    }

    [Fact]
    public async Task Handle_ShouldUpdateExistingWalletInformation_WhenUserConfigExists()
    {
        // Arrange
        var userId = 456;
        var originalWalletId = Guid.NewGuid();
        var newWalletId = Guid.NewGuid();

        // Create existing user config with wallet information
        var existingWalletInfo = UserWalletInformation.Create(userId, originalWalletId);
        var existingUserConfig = UserConfig.Create(existingWalletInfo);

        Context.UserConfigs.Add(existingUserConfig);
        await Context.SaveChangesAsync();

        var command = new AddUserPaymentWalletCommand(userId, newWalletId.ToString());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        var userConfig = await Context.UserConfigs
            .Include(x => x.WalletInformation)
            .FirstOrDefaultAsync(x => x.UserId == userId);

        userConfig.ShouldNotBeNull();
        userConfig.WalletInformation.PaymentWalletId.Value.ShouldBe(newWalletId);
        userConfig.WalletInformation.PaymentWalletId.Value.ShouldNotBe(originalWalletId);
    }

    [Fact]
    public async Task Handle_ShouldReturnError_WhenInvalidWalletIdProvided()
    {
        // Arrange
        var userId = 789;
        var invalidWalletId = "invalid-guid";
        var command = new AddUserPaymentWalletCommand(userId, invalidWalletId);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidWalletIdException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ShouldReturnError_WhenEmptyGuidProvided()
    {
        // Arrange
        var userId = 101;
        var emptyGuid = Guid.Empty.ToString();
        var command = new AddUserPaymentWalletCommand(userId, emptyGuid);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidWalletIdException>(
            () => _handler.Handle(command, CancellationToken.None));

        exception.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_ShouldCreateUserConfigWithDefaultValues_WhenCreatingNew()
    {
        // Arrange
        var userId = 202;
        var paymentWalletId = Guid.NewGuid().ToString();
        var command = new AddUserPaymentWalletCommand(userId, paymentWalletId);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        var userConfig = await Context.UserConfigs
            .Include(x => x.WalletInformation)
            .FirstOrDefaultAsync(x => x.UserId == userId);

        userConfig.ShouldNotBeNull();
        userConfig.AcceptorCode.ShouldBe(string.Empty);
        userConfig.IsCritical.ShouldBeFalse();
        userConfig.IsFree.ShouldBeFalse();
        userConfig.IsDepositActivate.ShouldBeFalse();
        userConfig.IsBanned.ShouldBeFalse();
        userConfig.WageType.ShouldBe(WageType.Fixed);
        userConfig.WageValue.ShouldBe(0);
        userConfig.Max.ShouldBe(0);
        userConfig.Min.ShouldBe(0);
        userConfig.MaxSettlementAmount.ShouldBe(0);
        userConfig.PlanType.ShouldBe(SettlementPlanType.Basic);
        userConfig.AllowSettlementRegistration.ShouldBeFalse();
        userConfig.AuthenticatorTotpEnabled.ShouldBeFalse();
        userConfig.AuthenticatorTotpSecretKey.ShouldBe(string.Empty);
    }

    [Fact]
    public async Task Handle_ShouldHandleCancellationToken_WhenCancellationRequested()
    {
        // Arrange
        var userId = 303;
        var paymentWalletId = Guid.NewGuid().ToString();
        var command = new AddUserPaymentWalletCommand(userId, paymentWalletId);

        var cancellationTokenSource = new CancellationTokenSource();
        await cancellationTokenSource.CancelAsync();

        // Act & Assert
        await Should.ThrowAsync<OperationCanceledException>(
            () => _handler.Handle(command, cancellationTokenSource.Token));
    }

    [Fact]
    public async Task Handle_ShouldNotCreateDuplicateUserConfig_WhenUserConfigAlreadyExists()
    {
        // Arrange
        var userId = 404;
        var originalWalletId = Guid.NewGuid();
        var newWalletId = Guid.NewGuid();

        // Create existing user config
        var existingWalletInfo = UserWalletInformation.Create(userId, originalWalletId);
        var existingUserConfig = UserConfig.Create(existingWalletInfo);

        Context.UserConfigs.Add(existingUserConfig);
        await Context.SaveChangesAsync();

        var command = new AddUserPaymentWalletCommand(userId, newWalletId.ToString());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsError.ShouldBeFalse();

        var userConfigs = await Context.UserConfigs
            .Where(x => x.UserId == userId)
            .ToListAsync();

        userConfigs.Count.ShouldBe(1); // Should not create duplicate
    }
}
