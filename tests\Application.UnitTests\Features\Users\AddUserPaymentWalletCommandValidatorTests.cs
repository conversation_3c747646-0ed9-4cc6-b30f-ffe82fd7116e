using FluentValidation.TestHelper;
using Shouldly;
using Zify.Settlement.Application.Features.UserConfigurations;

namespace PayPing.Settlement.Application.UnitTests.Features.Users;

public class AddUserPaymentWalletCommandValidatorTests
{
    private readonly AddUserPaymentWalletCommandValidator _validator;

    public AddUserPaymentWalletCommandValidatorTests()
    {
        _validator = new AddUserPaymentWalletCommandValidator();
    }

    [Fact]
    public void Validate_ShouldPass_WhenValidCommandProvided()
    {
        // Arrange
        var command = new AddUserPaymentWalletCommand(123, Guid.NewGuid().ToString());

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(-100)]
    public void Validate_ShouldFail_WhenUserIdIsZeroOrNegative(int userId)
    {
        // Arrange
        var command = new AddUserPaymentWalletCommand(userId, Guid.NewGuid().ToString());

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.UserId)
            .WithErrorMessage("User ID must be greater than 0");
    }

    [Fact]
    public void Validate_ShouldPass_WhenUserIdIsPositive()
    {
        // Arrange
        var command = new AddUserPaymentWalletCommand(1, Guid.NewGuid().ToString());

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.UserId);
    }

    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    public void Validate_ShouldFail_WhenPaymentWalletIdIsNullOrEmpty(string paymentWalletId)
    {
        // Arrange
        var command = new AddUserPaymentWalletCommand(123, paymentWalletId);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.PaymentWalletId)
            .WithErrorMessage("Payment wallet ID is required");
    }

    [Fact]
    public void Validate_ShouldFail_WhenPaymentWalletIdIsNull()
    {
        // Arrange
        var command = new AddUserPaymentWalletCommand(123, null!);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.PaymentWalletId)
            .WithErrorMessage("Payment wallet ID is required");
    }

    [Theory]
    [InlineData("invalid-guid")]
    [InlineData("12345")]
    [InlineData("not-a-guid-at-all")]
    [InlineData("123e4567-e89b-12d3-a456-42661417400")]  // Invalid format
    [InlineData("xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx")]
    public void Validate_ShouldFail_WhenPaymentWalletIdIsInvalidGuid(string invalidGuid)
    {
        // Arrange
        var command = new AddUserPaymentWalletCommand(123, invalidGuid);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.PaymentWalletId)
            .WithErrorMessage("Payment wallet ID must be a valid GUID");
    }

    [Fact]
    public void Validate_ShouldFail_WhenPaymentWalletIdIsEmptyGuid()
    {
        // Arrange
        var command = new AddUserPaymentWalletCommand(123, Guid.Empty.ToString());

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.PaymentWalletId)
            .WithErrorMessage("Payment wallet ID must be a valid GUID");
    }

    [Fact]
    public void Validate_ShouldPass_WhenPaymentWalletIdIsValidGuid()
    {
        // Arrange
        var validGuid = Guid.NewGuid().ToString();
        var command = new AddUserPaymentWalletCommand(123, validGuid);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.PaymentWalletId);
    }

    [Theory]
    [InlineData("123e4567-e89b-12d3-a456-************")]  // Valid GUID format
    [InlineData("A1B2C3D4-E5F6-7890-ABCD-EF1234567890")]  // Valid GUID with letters
    [InlineData("12345678-1234-1234-1234-123456789012")]   // Valid GUID format
    public void Validate_ShouldPass_WhenPaymentWalletIdIsValidGuidFormat(string validGuid)
    {
        // Arrange
        var command = new AddUserPaymentWalletCommand(123, validGuid);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.PaymentWalletId);
    }

    [Fact]
    public void Validate_ShouldHaveMultipleErrors_WhenBothFieldsAreInvalid()
    {
        // Arrange
        var command = new AddUserPaymentWalletCommand(-1, "invalid-guid");

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.UserId);
        result.ShouldHaveValidationErrorFor(x => x.PaymentWalletId);
        result.Errors.Count.ShouldBe(2);
    }

    [Fact]
    public void Validate_ShouldPass_WhenAllFieldsAreValid()
    {
        // Arrange
        var command = new AddUserPaymentWalletCommand(999, Guid.NewGuid().ToString());

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
        result.IsValid.ShouldBeTrue();
    }

    [Theory]
    [InlineData(1)]
    [InlineData(100)]
    [InlineData(999999)]
    [InlineData(int.MaxValue)]
    public void Validate_ShouldPass_WhenUserIdIsAnyPositiveValue(int userId)
    {
        // Arrange
        var command = new AddUserPaymentWalletCommand(userId, Guid.NewGuid().ToString());

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.UserId);
    }
}
