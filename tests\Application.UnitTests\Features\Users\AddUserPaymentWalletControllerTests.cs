using DispatchR.Requests;
using ErrorOr;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Shouldly;
using Zify.Settlement.Application.Features.UserConfigurations;

namespace PayPing.Settlement.Application.UnitTests.Features.Users;

public class AddUserPaymentWalletControllerTests
{
    private readonly Mock<IMediator> _mockMediator;
    private readonly AddUserPaymentWalletController _controller;

    public AddUserPaymentWalletControllerTests()
    {
        _mockMediator = new Mock<IMediator>();
        _controller = new AddUserPaymentWalletController();

        // Setup HttpContext for the controller
        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext()
        };

        // Use reflection to set the private _mediator field
        var mediatorField = typeof(AddUserPaymentWalletController)
            .BaseType?.GetField("_mediator", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        mediatorField?.SetValue(_controller, _mockMediator.Object);
    }

    [Fact]
    public async Task AddUserPaymentWallet_ShouldReturnOk_WhenCommandSucceeds()
    {
        // Arrange
        var userId = 123;
        var paymentWalletId = Guid.NewGuid().ToString();
        var request = new AddUserPaymentWalletRequest(paymentWalletId);

        _mockMediator
            .Setup(x => x.Send(It.IsAny<AddUserPaymentWalletCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success);

        // Act
        var result = await _controller.AddUserPaymentWallet(userId, request);

        // Assert
        result.ShouldBeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult?.Value.ShouldNotBeNull();
    }

    [Fact]
    public async Task AddUserPaymentWallet_ShouldCallMediatorWithCorrectCommand_WhenInvoked()
    {
        // Arrange
        var userId = 456;
        var paymentWalletId = Guid.NewGuid().ToString();
        var request = new AddUserPaymentWalletRequest(paymentWalletId);

        _mockMediator
            .Setup(x => x.Send(It.IsAny<AddUserPaymentWalletCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success);

        // Act
        await _controller.AddUserPaymentWallet(userId, request);

        // Assert
        _mockMediator.Verify(
            x => x.Send(
                It.Is<AddUserPaymentWalletCommand>(cmd =>
                    cmd.UserId == userId &&
                    cmd.PaymentWalletId == paymentWalletId),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task AddUserPaymentWallet_ShouldReturnProblem_WhenCommandFails()
    {
        // Arrange
        var userId = 789;
        var paymentWalletId = Guid.NewGuid().ToString();
        var request = new AddUserPaymentWalletRequest(paymentWalletId);
        var error = Error.Failure("Database.Error", "Failed to save changes");

        _mockMediator
            .Setup(x => x.Send(It.IsAny<AddUserPaymentWalletCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(error);

        // Act
        var result = await _controller.AddUserPaymentWallet(userId, request);

        // Assert
        result.ShouldBeOfType<ObjectResult>();
        var objectResult = result as ObjectResult;
        objectResult?.StatusCode.ShouldBe(500); // Internal server error for failure
    }

    [Fact]
    public async Task AddUserPaymentWallet_ShouldReturnValidationProblem_WhenValidationErrors()
    {
        // Arrange
        var userId = 101;
        var paymentWalletId = "invalid-guid";
        var request = new AddUserPaymentWalletRequest(paymentWalletId);
        var validationErrors = new List<Error>
        {
            Error.Validation("PaymentWalletId", "Payment wallet ID must be a valid GUID")
        };

        _mockMediator
            .Setup(x => x.Send(It.IsAny<AddUserPaymentWalletCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validationErrors);

        // Act
        var result = await _controller.AddUserPaymentWallet(userId, request);

        // Assert
        result.ShouldBeOfType<ObjectResult>();
    }

    [Fact]
    public async Task AddUserPaymentWallet_ShouldPassCancellationToken_WhenInvoked()
    {
        // Arrange
        var userId = 202;
        var paymentWalletId = Guid.NewGuid().ToString();
        var request = new AddUserPaymentWalletRequest(paymentWalletId);

        var cancellationTokenSource = new CancellationTokenSource();
        _controller.HttpContext.RequestAborted = cancellationTokenSource.Token;

        _mockMediator
            .Setup(x => x.Send(It.IsAny<AddUserPaymentWalletCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success);

        // Act
        await _controller.AddUserPaymentWallet(userId, request);

        // Assert
        _mockMediator.Verify(
            x => x.Send(
                It.IsAny<AddUserPaymentWalletCommand>(),
                cancellationTokenSource.Token),
            Times.Once);
    }

    [Fact]
    public async Task AddUserPaymentWallet_ShouldReturnConflictProblem_WhenConflictError()
    {
        // Arrange
        var userId = 404;
        var paymentWalletId = Guid.NewGuid().ToString();
        var request = new AddUserPaymentWalletRequest(paymentWalletId);
        var conflictError = Error.Conflict("User.AlreadyExists", "User wallet already configured");

        _mockMediator
            .Setup(x => x.Send(It.IsAny<AddUserPaymentWalletCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(conflictError);

        // Act
        var result = await _controller.AddUserPaymentWallet(userId, request);

        // Assert
        result.ShouldBeOfType<ObjectResult>();
        var objectResult = result as ObjectResult;
        objectResult?.StatusCode.ShouldBe(409); // Conflict
    }

    [Fact]
    public async Task AddUserPaymentWallet_ShouldReturnNotFoundProblem_WhenNotFoundError()
    {
        // Arrange
        var userId = 505;
        var paymentWalletId = Guid.NewGuid().ToString();
        var request = new AddUserPaymentWalletRequest(paymentWalletId);
        var notFoundError = Error.NotFound("User.NotFound", "User not found");

        _mockMediator
            .Setup(x => x.Send(It.IsAny<AddUserPaymentWalletCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(notFoundError);

        // Act
        var result = await _controller.AddUserPaymentWallet(userId, request);

        // Assert
        result.ShouldBeOfType<ObjectResult>();
        var objectResult = result as ObjectResult;
        objectResult?.StatusCode.ShouldBe(404); // Not Found
    }

    [Theory]
    [InlineData(1)]
    [InlineData(999)]
    [InlineData(int.MaxValue)]
    public async Task AddUserPaymentWallet_ShouldAcceptAnyPositiveUserId_WhenInvoked(int userId)
    {
        // Arrange
        var paymentWalletId = Guid.NewGuid().ToString();
        var request = new AddUserPaymentWalletRequest(paymentWalletId);

        _mockMediator
            .Setup(x => x.Send(It.IsAny<AddUserPaymentWalletCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success);

        // Act
        var result = await _controller.AddUserPaymentWallet(userId, request);

        // Assert
        result.ShouldBeOfType<OkObjectResult>();

        _mockMediator.Verify(
            x => x.Send(
                It.Is<AddUserPaymentWalletCommand>(cmd => cmd.UserId == userId),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }
}
