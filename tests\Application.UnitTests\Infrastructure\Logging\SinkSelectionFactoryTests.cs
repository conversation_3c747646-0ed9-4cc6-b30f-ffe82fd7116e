using Moq;
using Shouldly;
using Zify.Settlement.Application.Infrastructure.Configurations;
using Zify.Settlement.Application.Infrastructure.Logging;

namespace PayPing.Settlement.Application.UnitTests.Infrastructure.Logging;

public class SinkSelectionFactoryTests
{
    private readonly Mock<ISinkSelectionStrategy> _tcpStrategyMock;
    private readonly Mock<ISinkSelectionStrategy> _udpStrategyMock;
    private readonly SinkSelectionFactory _factory;

    public SinkSelectionFactoryTests()
    {
        _tcpStrategyMock = new Mock<ISinkSelectionStrategy>();
        _udpStrategyMock = new Mock<ISinkSelectionStrategy>();
        
        var strategies = new List<ISinkSelectionStrategy>
        {
            _tcpStrategyMock.Object,
            _udpStrategyMock.Object
        };
        
        _factory = new SinkSelectionFactory(strategies);
    }

    [Fact]
    public void Constructor_WithNullStrategies_ShouldThrowArgumentNullException()
    {
        // Arrange & Act & Assert
        Should.Throw<ArgumentNullException>(() => new SinkSelectionFactory(null!));
    }

    [Fact]
    public void GetStrategy_WithTcpType_ShouldReturnTcpStrategy()
    {
        // Arrange
        var options = new ElasticsearchOptions { LogstashType = "TCP" };
        _tcpStrategyMock.Setup(x => x.CanHandle(options)).Returns(true);
        _udpStrategyMock.Setup(x => x.CanHandle(options)).Returns(false);

        // Act
        var result = _factory.GetStrategy(options);

        // Assert
        result.ShouldBe(_tcpStrategyMock.Object);
        _tcpStrategyMock.Verify(x => x.CanHandle(options), Times.Once);
    }

    [Fact]
    public void GetStrategy_WithUdpType_ShouldReturnUdpStrategy()
    {
        // Arrange
        var options = new ElasticsearchOptions { LogstashType = "UDP" };
        _tcpStrategyMock.Setup(x => x.CanHandle(options)).Returns(false);
        _udpStrategyMock.Setup(x => x.CanHandle(options)).Returns(true);

        // Act
        var result = _factory.GetStrategy(options);

        // Assert
        result.ShouldBe(_udpStrategyMock.Object);
        _udpStrategyMock.Verify(x => x.CanHandle(options), Times.Once);
    }

    [Fact]
    public void GetStrategy_WithUnsupportedType_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var options = new ElasticsearchOptions { LogstashType = "UNSUPPORTED" };
        _tcpStrategyMock.Setup(x => x.CanHandle(options)).Returns(false);
        _udpStrategyMock.Setup(x => x.CanHandle(options)).Returns(false);

        // Act & Assert
        var exception = Should.Throw<InvalidOperationException>(() => _factory.GetStrategy(options));
        exception.Message.ShouldContain("No sink selection strategy found for Logstash type: UNSUPPORTED");
        exception.Message.ShouldContain("Supported types are: TCP, UDP");
    }

    [Fact]
    public void GetStrategy_WithNullOptions_ShouldThrowArgumentNullException()
    {
        // Arrange & Act & Assert
        Should.Throw<ArgumentNullException>(() => _factory.GetStrategy(null!));
    }

    [Fact]
    public void GetAllStrategies_ShouldReturnAllRegisteredStrategies()
    {
        // Act
        var result = _factory.GetAllStrategies().ToList();

        // Assert
        result.Count.ShouldBe(2);
        result.ShouldContain(_tcpStrategyMock.Object);
        result.ShouldContain(_udpStrategyMock.Object);
    }

    [Fact]
    public void GetStrategy_WithCaseInsensitiveTcpType_ShouldReturnTcpStrategy()
    {
        // Arrange
        var options = new ElasticsearchOptions { LogstashType = "tcp" };
        _tcpStrategyMock.Setup(x => x.CanHandle(options)).Returns(true);
        _udpStrategyMock.Setup(x => x.CanHandle(options)).Returns(false);

        // Act
        var result = _factory.GetStrategy(options);

        // Assert
        result.ShouldBe(_tcpStrategyMock.Object);
    }

    [Fact]
    public void GetStrategy_WithCaseInsensitiveUdpType_ShouldReturnUdpStrategy()
    {
        // Arrange
        var options = new ElasticsearchOptions { LogstashType = "udp" };
        _tcpStrategyMock.Setup(x => x.CanHandle(options)).Returns(false);
        _udpStrategyMock.Setup(x => x.CanHandle(options)).Returns(true);

        // Act
        var result = _factory.GetStrategy(options);

        // Assert
        result.ShouldBe(_udpStrategyMock.Object);
    }
}
