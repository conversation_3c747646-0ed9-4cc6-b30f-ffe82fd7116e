using Microsoft.AspNetCore.Http;
using Moq;
using Shouldly;
using System.Security.Claims;
using Zify.Settlement.Application.Infrastructure.Services;

namespace PayPing.Settlement.Application.UnitTests.Infrastructure.Services;

public class CurrentUserServiceTests
{
    [Fact]
    public void UserId_WhenNoHttpContext_ReturnsNull()
    {
        // Arrange
        var httpContextAccessor = new Mock<IHttpContextAccessor>();
        httpContextAccessor.Setup(x => x.HttpContext).Returns((HttpContext?)null);
        var service = new CurrentUserService(httpContextAccessor.Object);

        // Act
        var result = service.UserId;

        // Assert
        result.ShouldBeNull();
    }

    [Fact]
    public void UserId_WhenNoUser_ReturnsNull()
    {
        // Arrange
        var httpContext = new Mock<HttpContext>();
        httpContext.Setup(x => x.User).Returns(new ClaimsPrincipal());

        var httpContextAccessor = new Mock<IHttpContextAccessor>();
        httpContextAccessor.Setup(x => x.HttpContext).Returns(httpContext.Object);

        var service = new CurrentUserService(httpContextAccessor.Object);

        // Act
        var result = service.UserId;

        // Assert
        result.ShouldBeNull();
    }

    [Fact]
    public void UserId_WhenValidSubClaim_ReturnsUserId()
    {
        // Arrange
        var claims = new List<Claim>
        {
            new("sub", "456")
        };
        var identity = new ClaimsIdentity(claims);
        var principal = new ClaimsPrincipal(identity);

        var httpContext = new Mock<HttpContext>();
        httpContext.Setup(x => x.User).Returns(principal);

        var httpContextAccessor = new Mock<IHttpContextAccessor>();
        httpContextAccessor.Setup(x => x.HttpContext).Returns(httpContext.Object);

        var service = new CurrentUserService(httpContextAccessor.Object);

        // Act
        var result = service.UserId;

        // Assert
        result.ShouldBe(456);
    }

    [Fact]
    public void UserId_WhenInvalidClaimValue_ReturnsNull()
    {
        // Arrange
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, "invalid")
        };
        var identity = new ClaimsIdentity(claims);
        var principal = new ClaimsPrincipal(identity);

        var httpContext = new Mock<HttpContext>();
        httpContext.Setup(x => x.User).Returns(principal);

        var httpContextAccessor = new Mock<IHttpContextAccessor>();
        httpContextAccessor.Setup(x => x.HttpContext).Returns(httpContext.Object);

        var service = new CurrentUserService(httpContextAccessor.Object);

        // Act
        var result = service.UserId;

        // Assert
        result.ShouldBeNull();
    }
}
