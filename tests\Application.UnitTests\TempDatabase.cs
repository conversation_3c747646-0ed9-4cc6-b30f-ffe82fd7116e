﻿using Microsoft.EntityFrameworkCore;
using ThrowawayDb.Postgres;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace PayPing.Settlement.Application.UnitTests;
public class TempDatabase : IAsyncLifetime
{
    private static class Settings
    {
        public const string Username = "postgres";
        public const string Password = "admin";
        public const string Host = "localhost";
    }

    private readonly ThrowawayDatabase _database;
    protected readonly ApplicationDbContext Context;

    protected TempDatabase()
    {
        _database = ThrowawayDatabase.Create(
            Settings.Username,
            Settings.Password,
            Settings.Host);

        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseNpgsql(_database.ConnectionString)
            .Options;

        Context = new ApplicationDbContext(options);
    }

    public async Task InitializeAsync()
    {
        await Context.Database.MigrateAsync();
    }

    public Task DisposeAsync()
    {
        Context.Dispose();
        _database.Dispose();
        return Task.CompletedTask;
    }
}
